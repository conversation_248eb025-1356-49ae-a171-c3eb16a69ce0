# Server Configuration
server.port=8084

# Socket Server Configuration
socket.server.port=8085

# Log File Configuration
log.file.path=HNX_20250625.log
log.file.read.delay=1000
log.file.replay.mode=true

# Logging Configuration
logging.level.com.example.socketserver=INFO
logging.level.root=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%level] %logger{36} - %msg%n

# Application Configuration
spring.application.name=socket-server
spring.main.banner-mode=console

# Async Configuration
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=50
spring.task.execution.pool.queue-capacity=100

# Multicast Configuration
multicast.enabled=true
multicast.group.address=***************
multicast.port=9000
multicast.ttl=1
multicast.heartbeat.interval=30000
