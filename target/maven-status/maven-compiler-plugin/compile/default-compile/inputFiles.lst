/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/controller/ServerStatusController.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/SocketServerApplication.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/service/HeartbeatService.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/service/SocketServerService.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/service/MulticastServerService.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/client/MulticastClient.java
/Users/<USER>/Documents/mdds-server/socket-java/src/main/java/com/example/socketserver/service/FileReaderService.java
