# HNX Socket Server

Một ứng dụng Java Spring Boot 3 với Java 17 có tính năng server socket để đọc file HNX_20250620.log và emit từng dòng đến các client kết nối.

## Tính năng

- **Socket Server**: Lắng nghe trên port 8888, cho phép nhiều client kết nối đồng thời
- **File Reader**: Đọc file HNX_20250620.log và stream từng dòng đến tất cả client
- **REST API**: Cung cấp API để monitor trạng thái server
- **Real-time Streaming**: Emit dữ liệu FIX protocol từ HNX theo thời gian thực
- **Auto Reconnection**: Tự động xử lý kết nối và ngắt kết nối của client

## Cấu trúc Project

```
socket-java/
├── src/
│   ├── main/
│   │   ├── java/com/example/socketserver/
│   │   │   ├── SocketServerApplication.java          # Main application
│   │   │   ├── controller/
│   │   │   │   └── ServerStatusController.java       # REST API controller
│   │   │   └── service/
│   │   │       ├── SocketServerService.java          # Socket server logic
│   │   │       └── FileReaderService.java            # File reading logic
│   │   └── resources/
│   │       └── application.properties                # Configuration
│   └── test/
├── pom.xml                                           # Maven dependencies
├── HNX_20250620.log                                  # Log file to read
└── README.md
```

## Cấu hình

File `application.properties`:

```properties
# Server Configuration
server.port=8088

# Socket Server Configuration
socket.server.port=8888

# Log File Configuration
log.file.path=HNX_20250620.log
log.file.read.delay=100
log.file.replay.mode=true
```

## Cách chạy

### 1. Compile và Test

```bash
mvn clean compile
mvn test
```

### 2. Chạy Application

```bash
mvn spring-boot:run
```

Application sẽ khởi động:
- HTTP Server trên port 8080
- Socket Server trên port 8888

### 3. Kết nối Socket Client

Sử dụng telnet để test:

```bash
telnet localhost 8888
```

Hoặc sử dụng bất kỳ socket client nào khác.

### 4. Monitor Status

Kiểm tra trạng thái server:

```bash
curl http://localhost:8080/api/status
```

Response:
```json
{
  "socketServer": "running",
  "activeClients": 1,
  "fileReaderActive": true,
  "timestamp": 1750677913414
}
```

## API Endpoints

### GET /api/status
Trả về trạng thái tổng quan của server

### GET /api/status/clients
Trả về thông tin về số lượng client đang kết nối

## Dữ liệu Output

Mỗi dòng được emit có format:
```
[Line X] <original_log_line>
```

Ví dụ:
```
[Line 302] 2025-06-20 08:00:18,620 [INFO] __main__: Received (**********:55612) → 8=FIX.4.49=45935=d49=VNMGW56=9999934=29952=...
```

## Tính năng nâng cao

- **Replay Mode**: Đọc file từ đầu và stream tất cả dữ liệu
- **Watch Mode**: Theo dõi file để phát hiện dữ liệu mới được thêm vào
- **Multi-client Support**: Hỗ trợ nhiều client kết nối đồng thời
- **Error Handling**: Xử lý lỗi kết nối và tự động cleanup
- **Configurable Delay**: Có thể điều chỉnh tốc độ stream dữ liệu

## Requirements

- Java 17+
- Maven 3.6+
- Spring Boot 3.2.0

## Logs

Application sử dụng SLF4J với Logback để ghi log. Có thể theo dõi:
- Kết nối/ngắt kết nối của client
- Tiến trình đọc file
- Lỗi và cảnh báo

## Troubleshooting

1. **Port đã được sử dụng**: Thay đổi port trong `application.properties`
2. **File không tìm thấy**: Đảm bảo file `HNX_20250620.log` có trong thư mục gốc
3. **Client không nhận được dữ liệu**: Kiểm tra firewall và network connectivity
