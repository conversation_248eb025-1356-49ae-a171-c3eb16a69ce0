package com.example.socketserver.controller;

import com.example.socketserver.service.FileReaderService;
import com.example.socketserver.service.MulticastServerService;
import com.example.socketserver.service.SocketServerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/status")
public class ServerStatusController {

    @Autowired
    private SocketServerService socketServerService;

    @Autowired
    private FileReaderService fileReaderService;

    @Autowired
    private MulticastServerService multicastServerService;

    @GetMapping
    public ResponseEntity<Map<String, Object>> getServerStatus() {
        Map<String, Object> status = new HashMap<>();

        status.put("socketServer", "running");
        status.put("activeClients", socketServerService.getActiveClientCount());
        status.put("fileReaderActive", fileReaderService.isReading());
        status.put("multicastServer", multicastServerService.isRunning() ? "running" : "stopped");
        status.put("timestamp", System.currentTimeMillis());

        // Multicast details
        Map<String, Object> multicastInfo = new HashMap<>();
        multicastInfo.put("groupAddress", multicastServerService.getMulticastGroupAddress());
        multicastInfo.put("port", multicastServerService.getMulticastPort());
        multicastInfo.put("ttl", multicastServerService.getTimeToLive());
        multicastInfo.put("messagesSent", multicastServerService.getMessagesSent());
        multicastInfo.put("bytesTransferred", multicastServerService.getBytesTransferred());
        status.put("multicast", multicastInfo);

        return ResponseEntity.ok(status);
    }

    @GetMapping("/clients")
    public ResponseEntity<Map<String, Object>> getClientInfo() {
        Map<String, Object> clientInfo = new HashMap<>();
        
        clientInfo.put("activeClients", socketServerService.getActiveClientCount());
        clientInfo.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(clientInfo);
    }
}
