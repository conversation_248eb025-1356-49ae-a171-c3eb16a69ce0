package com.example.socketserver.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Example MulticastSocket Client to receive HNX data
 * This is a standalone client that can be run independently
 */
public class MulticastClient {

    private static final Logger logger = LoggerFactory.getLogger(MulticastClient.class);

    private final String multicastGroupAddress;
    private final int multicastPort;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong bytesReceived = new AtomicLong(0);

    private MulticastSocket multicastSocket;
    private InetAddress group;

    public MulticastClient(String multicastGroupAddress, int multicastPort) {
        this.multicastGroupAddress = multicastGroupAddress;
        this.multicastPort = multicastPort;
    }

    public void start() {
        try {
            // Create multicast socket
            multicastSocket = new MulticastSocket(multicastPort);
            group = InetAddress.getByName(multicastGroupAddress);

            if (!group.isMulticastAddress()) {
                throw new IllegalArgumentException("Invalid multicast address: " + multicastGroupAddress);
            }

            // Join multicast group
            multicastSocket.joinGroup(group);
            isRunning.set(true);

            logger.info("MulticastClient started - Listening on {}:{}", multicastGroupAddress, multicastPort);

            // Start receiving messages
            receiveMessages();

        } catch (IOException e) {
            logger.error("Failed to start multicast client", e);
            stop();
        }
    }

    private void receiveMessages() {
        byte[] buffer = new byte[8192]; // 8KB buffer

        while (isRunning.get()) {
            try {
                DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                multicastSocket.receive(packet);

                // Process received message
                String message = new String(packet.getData(), 0, packet.getLength(), StandardCharsets.UTF_8);
                processMessage(message, packet.getAddress(), packet.getPort());

                // Update statistics
                messagesReceived.incrementAndGet();
                bytesReceived.addAndGet(packet.getLength());

            } catch (SocketTimeoutException e) {
                // Timeout is normal, continue listening
                continue;
            } catch (IOException e) {
                if (isRunning.get()) {
                    logger.error("Error receiving multicast message", e);
                }
                break;
            }
        }
    }

    private void processMessage(String message, InetAddress senderAddress, int senderPort) {
        try {
            logger.info("Received from {}:{} - Length: {} bytes", 
                       senderAddress.getHostAddress(), senderPort, message.length());
            
            // Parse JSON message if possible
            if (message.startsWith("{") && message.endsWith("}")) {
                logger.info("JSON Message: {}", message);
                
                // Extract message type for different handling
                if (message.contains("\"type\":\"HEARTBEAT\"")) {
                    handleHeartbeat(message);
                } else if (message.contains("\"type\":\"MARKET_DATA\"")) {
                    handleMarketData(message);
                } else if (message.contains("\"type\":\"SHUTDOWN\"")) {
                    handleShutdown(message);
                } else {
                    handleGenericMessage(message);
                }
            } else {
                logger.info("Raw Message: {}", message);
            }

        } catch (Exception e) {
            logger.error("Error processing message: {}", message, e);
        }
    }

    private void handleHeartbeat(String message) {
        logger.debug("Received heartbeat: {}", message);
    }

    private void handleMarketData(String message) {
        logger.info("Market Data: {}", message.substring(0, Math.min(200, message.length())));
    }

    private void handleShutdown(String message) {
        logger.warn("Server shutdown notification: {}", message);
        stop();
    }

    private void handleGenericMessage(String message) {
        logger.info("Generic message: {}", message.substring(0, Math.min(100, message.length())));
    }

    public void stop() {
        if (isRunning.compareAndSet(true, false)) {
            try {
                if (multicastSocket != null) {
                    if (group != null) {
                        multicastSocket.leaveGroup(group);
                    }
                    multicastSocket.close();
                }

                logger.info("MulticastClient stopped. Messages received: {}, Bytes received: {}", 
                           messagesReceived.get(), bytesReceived.get());

            } catch (IOException e) {
                logger.error("Error stopping multicast client", e);
            }
        }
    }

    public boolean isRunning() {
        return isRunning.get();
    }

    public long getMessagesReceived() {
        return messagesReceived.get();
    }

    public long getBytesReceived() {
        return bytesReceived.get();
    }

    // Main method for standalone execution
    public static void main(String[] args) {
        String groupAddress = args.length > 0 ? args[0] : "***************";
        int port = args.length > 1 ? Integer.parseInt(args[1]) : 9000;

        MulticastClient client = new MulticastClient(groupAddress, port);

        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down MulticastClient...");
            client.stop();
        }));

        // Start client
        client.start();
    }
}
