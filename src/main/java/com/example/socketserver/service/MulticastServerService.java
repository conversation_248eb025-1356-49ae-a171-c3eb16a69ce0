package com.example.socketserver.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

@Service
public class MulticastServerService {

    private static final Logger logger = LoggerFactory.getLogger(MulticastServerService.class);

    @Value("${multicast.group.address:***************}")
    private String multicastGroupAddress;

    @Value("${multicast.port:9000}")
    private int multicastPort;

    @Value("${multicast.ttl:1}")
    private int timeToLive;

    @Value("${multicast.enabled:true}")
    private boolean multicastEnabled;

    private MulticastSocket multicastSocket;
    private InetAddress group;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong bytesTransferred = new AtomicLong(0);

    @PostConstruct
    public void startMulticastServer() {
        if (!multicastEnabled) {
            logger.info("Multicast server is disabled");
            return;
        }

        try {
            // Create multicast socket
            multicastSocket = new MulticastSocket();
            multicastSocket.setTimeToLive(timeToLive);
            
            // Set multicast group
            group = InetAddress.getByName(multicastGroupAddress);
            
            if (!group.isMulticastAddress()) {
                throw new IllegalArgumentException("Invalid multicast address: " + multicastGroupAddress);
            }

            isRunning.set(true);
            logger.info("Multicast server started - Group: {}, Port: {}, TTL: {}", 
                       multicastGroupAddress, multicastPort, timeToLive);

        } catch (IOException e) {
            logger.error("Failed to start multicast server", e);
            isRunning.set(false);
        }
    }

    public void broadcastMessage(String message) {
        if (!isRunning.get() || multicastSocket == null) {
            logger.debug("Multicast server not running, skipping broadcast");
            return;
        }

        try {
            // Add timestamp and format message
            String [] arrFormat = message.split("→");
            byte[] messageBytes = "".getBytes(StandardCharsets.UTF_8);
            if(arrFormat.length > 1) {
                messageBytes = arrFormat[1].trim().getBytes(StandardCharsets.UTF_8);
            }

            // Create datagram packet
            DatagramPacket packet = new DatagramPacket(
                messageBytes, 
                messageBytes.length, 
                group, 
                multicastPort
            );
            
            // Send packet
            multicastSocket.send(packet);
            
            // Update statistics
            messagesSent.incrementAndGet();
            bytesTransferred.addAndGet(messageBytes.length);
            
            logger.debug("Broadcasted message: {} bytes to {}:{}", 
                        messageBytes.length, multicastGroupAddress, multicastPort);
            
        } catch (IOException e) {
            logger.error("Error broadcasting message", e);
        }
    }

    public void broadcastHNXData(String hnxLogLine) {
        if (!isRunning.get()) {
            return;
        }

        try {
            // Create HNX-specific message format
//            String hnxMessage = createHNXMessage(hnxLogLine);
//            broadcastMessage(hnxLogLine);
            broadcastMessage(hnxLogLine);

        } catch (Exception e) {
            logger.error("Error broadcasting HNX data", e);
        }
    }

    private String formatMessage(String message) {
        long timestamp = System.currentTimeMillis();
//        return String.format("{\"timestamp\":%d,\"source\":\"HNX_MULTICAST\",\"data\":\"%s\"}",
//                           timestamp, escapeJson(message));
        return message;
    }

    private String createHNXMessage(String hnxLogLine) {
        long timestamp = System.currentTimeMillis();
        long sequenceNumber = messagesSent.get() + 1;
        
        return String.format(
            "{\"timestamp\":%d,\"sequence\":%d,\"source\":\"HNX\",\"type\":\"MARKET_DATA\",\"data\":\"%s\"}", 
            timestamp, sequenceNumber, escapeJson(hnxLogLine)
        );
    }

    private String escapeJson(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\\", "\\\\")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }

    public void sendHeartbeat() {
        if (!isRunning.get()) {
            return;
        }

        try {
            String heartbeat = String.format(
                "{\"timestamp\":%d,\"type\":\"HEARTBEAT\",\"source\":\"HNX_MULTICAST_SERVER\",\"stats\":{\"messagesSent\":%d,\"bytesTransferred\":%d}}", 
                System.currentTimeMillis(), messagesSent.get(), bytesTransferred.get()
            );
            
            broadcastMessage(heartbeat);
            logger.debug("Sent heartbeat to multicast group");

        } catch (Exception e) {
            logger.error("Error sending heartbeat", e);
        }
    }

    public boolean isRunning() {
        return isRunning.get();
    }

    public long getMessagesSent() {
        return messagesSent.get();
    }

    public long getBytesTransferred() {
        return bytesTransferred.get();
    }

    public String getMulticastGroupAddress() {
        return multicastGroupAddress;
    }

    public int getMulticastPort() {
        return multicastPort;
    }

    public int getTimeToLive() {
        return timeToLive;
    }

    @PreDestroy
    public void stopMulticastServer() {
        if (isRunning.compareAndSet(true, false)) {
            try {
                // Send shutdown notification
                String shutdownMessage = String.format(
                    "{\"timestamp\":%d,\"type\":\"SHUTDOWN\",\"source\":\"HNX_MULTICAST_SERVER\",\"message\":\"Server shutting down\"}", 
                    System.currentTimeMillis()
                );
                broadcastMessage(shutdownMessage);
                
                // Close socket
                if (multicastSocket != null && !multicastSocket.isClosed()) {
                    multicastSocket.close();
                }
                
                logger.info("Multicast server stopped. Total messages sent: {}, Total bytes: {}", 
                           messagesSent.get(), bytesTransferred.get());
                
            } catch (Exception e) {
                logger.error("Error stopping multicast server", e);
            }
        }
    }
}
