package com.example.socketserver.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.io.*;
import java.nio.file.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

@Service
public class FileReaderService {

    private static final Logger logger = LoggerFactory.getLogger(FileReaderService.class);

    @Value("${log.file.path:HNX_20250620.log}")
    private String logFilePath;

    @Value("${log.file.read.delay:100}")
    private long readDelayMs;

    @Value("${log.file.replay.mode:true}")
    private boolean replayMode;

    private final AtomicBoolean isReading = new AtomicBoolean(false);
    private Consumer<String> messageConsumer;

    @Autowired
    private MulticastServerService multicastServerService;

    @Async
    public void startReading(Consumer<String> messageConsumer) {
        if (isReading.compareAndSet(false, true)) {
            this.messageConsumer = messageConsumer;
            logger.info("Starting to read log file: {}", logFilePath);
            
            if (replayMode) {
                readFileFromBeginning();
            } else {
                watchFileForNewLines();
            }
        } else {
            logger.warn("File reading is already in progress");
        }
    }

    private void readFileFromBeginning() {
        try {
            Path path = Paths.get(logFilePath);
            
            if (!Files.exists(path)) {
                logger.error("Log file not found: {}", logFilePath);
                return;
            }

            logger.info("Reading file from beginning in replay mode");
            
            try (BufferedReader reader = Files.newBufferedReader(path)) {
                String line;
                int lineCount = 0;
                
                while ((line = reader.readLine()) != null && isReading.get()) {
                    lineCount++;
                    
                    if (line.trim().isEmpty()) {
                        continue;
                    }
                    
                    // Emit the line to all connected clients
                    if (messageConsumer != null) {
                        String formattedMessage = String.format("[Line %d] %s", lineCount, line);
                        String [] arrFormat = formattedMessage.split("→");
                        messageConsumer.accept(arrFormat[1].trim());
                        logger.debug("Emitted line {}: {}", lineCount, line.substring(0, Math.min(100, line.length())));
                    }

                    // Broadcast to multicast group
                    if (multicastServerService != null && multicastServerService.isRunning()) {
                        multicastServerService.broadcastHNXData(line);
                    }
                    // Add delay between lines to simulate real-time streaming
                    if (readDelayMs > 0) {
                        Thread.sleep(readDelayMs);
                    }
                }
                
                logger.info("Finished reading file. Total lines processed: {}", lineCount);
                
                // After finishing the file, optionally start watching for new lines
                if (isReading.get()) {
                    logger.info("Switching to watch mode for new lines...");
                    watchFileForNewLines();
                }
                
            } catch (IOException e) {
                logger.error("Error reading log file", e);
            } catch (InterruptedException e) {
                logger.info("File reading interrupted");
                Thread.currentThread().interrupt();
            }
            
        } catch (Exception e) {
            logger.error("Unexpected error in file reading", e);
        }
    }

    private void watchFileForNewLines() {
        try {
            Path path = Paths.get(logFilePath);
            Path parentDir = path.getParent();
            
            if (parentDir == null) {
                parentDir = Paths.get(".");
            }
            
            logger.info("Watching for new lines in file: {}", logFilePath);
            
            try (WatchService watchService = FileSystems.getDefault().newWatchService()) {
                parentDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);
                
                // Keep track of file position
                long lastPosition = Files.exists(path) ? Files.size(path) : 0;
                
                while (isReading.get()) {
                    WatchKey key = watchService.poll(1, java.util.concurrent.TimeUnit.SECONDS);

                    if (key != null) {
                        for (WatchEvent<?> event : key.pollEvents()) {
                            if (event.context().toString().equals(path.getFileName().toString())) {
                                lastPosition = readNewLines(path, lastPosition);
                            }
                        }
                        key.reset();
                    }
                }
                
            } catch (IOException e) {
                logger.error("Error watching file for changes", e);
            }
            
        } catch (Exception e) {
            logger.error("Unexpected error in file watching", e);
        }
    }

    private long readNewLines(Path path, long fromPosition) {
        try {
            if (!Files.exists(path)) {
                return fromPosition;
            }
            
            long currentSize = Files.size(path);
            if (currentSize <= fromPosition) {
                return fromPosition;
            }
            
            try (RandomAccessFile file = new RandomAccessFile(path.toFile(), "r")) {
                file.seek(fromPosition);
                
                String line;
                while ((line = file.readLine()) != null && isReading.get()) {
                    if (!line.trim().isEmpty() && messageConsumer != null) {
                        String formattedMessage = String.format("[New] %s", line);
                        String [] arrFormat = formattedMessage.split("→");
                        messageConsumer.accept(arrFormat[1].trim());                        logger.debug("Emitted new line: {}", line.substring(0, Math.min(100, line.length())));
                    }
                }
                
                return file.getFilePointer();
            }
            
        } catch (IOException e) {
            logger.error("Error reading new lines from file", e);
            return fromPosition;
        }
    }

    public void stopReading() {
        if (isReading.compareAndSet(true, false)) {
            logger.info("Stopping file reading");
        }
    }

    public boolean isReading() {
        return isReading.get();
    }

    @PreDestroy
    public void cleanup() {
        stopReading();
    }
}
