package com.example.socketserver.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class HeartbeatService {

    private static final Logger logger = LoggerFactory.getLogger(HeartbeatService.class);

    @Autowired
    private MulticastServerService multicastServerService;

    @Value("${multicast.heartbeat.enabled:true}")
    private boolean heartbeatEnabled;

    @Scheduled(fixedRateString = "${multicast.heartbeat.interval:30000}")
    public void sendHeartbeat() {
        if (heartbeatEnabled && multicastServerService != null && multicastServerService.isRunning()) {
            try {
                multicastServerService.sendHeartbeat();
                logger.debug("Heartbeat sent to multicast group");
            } catch (Exception e) {
                logger.error("Error sending heartbeat", e);
            }
        }
    }
}
