package com.example.socketserver.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class SocketServerService {

    private static final Logger logger = LoggerFactory.getLogger(SocketServerService.class);

    @Value("${socket.server.port:8888}")
    private int serverPort;

    @Autowired
    private FileReaderService fileReaderService;

    private ServerSocket serverSocket;
    private ExecutorService executorService;
    private final ConcurrentHashMap<String, PrintWriter> clients = new ConcurrentHashMap<>();
    private final AtomicInteger clientCounter = new AtomicInteger(0);
    private volatile boolean running = false;

    @PostConstruct
    public void startServer() {
        try {
            serverSocket = new ServerSocket(serverPort);
            executorService = Executors.newCachedThreadPool();
            running = true;

            logger.info("Socket server started on port: {}", serverPort);

            // Start accepting connections in a separate thread
            executorService.submit(this::acceptConnections);

            // Start file reading and broadcasting
            fileReaderService.startReading(this::broadcastToAllClients);

        } catch (IOException e) {
            logger.error("Failed to start socket server", e);
        }
    }

    private void acceptConnections() {
        while (running && !serverSocket.isClosed()) {
            try {
                Socket clientSocket = serverSocket.accept();
                String clientId = "client-" + clientCounter.incrementAndGet();
                
                logger.info("New client connected: {} from {}", clientId, clientSocket.getRemoteSocketAddress());
                
                executorService.submit(() -> handleClient(clientSocket, clientId));
                
            } catch (IOException e) {
                if (running) {
                    logger.error("Error accepting client connection", e);
                }
            }
        }
    }

    private void handleClient(Socket clientSocket, String clientId) {
        try (PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {
            clients.put(clientId, out);
            
            // Send welcome message
            out.println("Connected to HNX Log Server. Client ID: " + clientId);
            
            // Keep connection alive and handle client disconnection
            while (!clientSocket.isClosed() && clientSocket.isConnected()) {
                Thread.sleep(1000);
            }
            
        } catch (Exception e) {
            logger.error("Error handling client {}", clientId, e);
        } finally {
            clients.remove(clientId);
            try {
                clientSocket.close();
            } catch (IOException e) {
                logger.error("Error closing client socket for {}", clientId, e);
            }
            logger.info("Client disconnected: {}", clientId);
        }
    }

    public void broadcastToAllClients(String message) {
        clients.forEach((clientId, writer) -> {
            try {
                writer.println(message);
                if (writer.checkError()) {
                    logger.warn("Error writing to client {}, removing from active clients", clientId);
                    clients.remove(clientId);
                }
            } catch (Exception e) {
                logger.error("Error broadcasting to client {}", clientId, e);
                clients.remove(clientId);
            }
        });
    }

    public int getActiveClientCount() {
        return clients.size();
    }

    @PreDestroy
    public void stopServer() {
        running = false;
        
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
            
            if (executorService != null) {
                executorService.shutdown();
            }
            
            // Close all client connections
            clients.forEach((clientId, writer) -> {
                writer.println("Server shutting down...");
                writer.close();
            });
            clients.clear();
            
            logger.info("Socket server stopped");
            
        } catch (IOException e) {
            logger.error("Error stopping socket server", e);
        }
    }
}
