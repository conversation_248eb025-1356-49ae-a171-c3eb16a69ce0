import java.io.IOException;
import java.net.*;
import java.nio.charset.StandardCharsets;

/**
 * Simple MulticastSocket Client without dependencies
 */
public class SimpleMulticastClient {

    private static final String MULTICAST_GROUP = "***************";
    private static final int MULTICAST_PORT = 9000;

    public static void main(String[] args) {
        String groupAddress = args.length > 0 ? args[0] : MULTICAST_GROUP;
        int port = args.length > 1 ? Integer.parseInt(args[1]) : MULTICAST_PORT;

        System.out.println("Starting MulticastClient - Group: " + groupAddress + ", Port: " + port);

        try (MulticastSocket socket = new MulticastSocket(port)) {
            InetAddress group = InetAddress.getByName(groupAddress);
            
            if (!group.isMulticastAddress()) {
                System.err.println("Invalid multicast address: " + groupAddress);
                return;
            }

            // Join multicast group
            socket.joinGroup(group);
            System.out.println("Joined multicast group: " + groupAddress);

            byte[] buffer = new byte[8192];
            int messageCount = 0;

            while (true) {
                try {
                    DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                    socket.receive(packet);

                    String message = new String(packet.getData(), 0, packet.getLength(), StandardCharsets.UTF_8);
                    messageCount++;

                    System.out.printf("[%d] From %s:%d - %d bytes\n", 
                                    messageCount, 
                                    packet.getAddress().getHostAddress(), 
                                    packet.getPort(), 
                                    packet.getLength());

                    // Print first 200 characters of message
                    String preview = message.length() > 200 ? message.substring(0, 200) + "..." : message;
                    System.out.println("Message: " + preview);
                    System.out.println("---");

                    // Stop after 10 messages for demo
                    if (messageCount >= 10) {
                        System.out.println("Received 10 messages, stopping...");
                        break;
                    }

                } catch (SocketTimeoutException e) {
                    System.out.println("Timeout waiting for messages...");
                    continue;
                } catch (IOException e) {
                    System.err.println("Error receiving message: " + e.getMessage());
                    break;
                }
            }

            // Leave multicast group
            socket.leaveGroup(group);
            System.out.println("Left multicast group and closed socket");

        } catch (IOException e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
